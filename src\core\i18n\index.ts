import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

// Cache for loaded translations
const translationCache = new Map<string, Record<string, unknown>>()

async function loadTranslation(locale: 'en' | 'ja'): Promise<Record<string, unknown>> {
	if (translationCache.has(locale)) {
		return translationCache.get(locale)!
	}

	try {
		// Try to load from build directory first (for runtime updates)
		const response = await fetch(`/locales/${locale}.json`)
		if (response.ok) {
			const translation = await response.json()
			translationCache.set(locale, translation)
			return translation
		}
	} catch (error) {
		console.warn(
			`Failed to load locale ${locale} from build directory, falling back to bundled version`,
			error,
		)
	}

	// Fallback to bundled versions
	try {
		const translation = await import(`./locales/${locale}.json`)
		const translationData = translation.default
		translationCache.set(locale, translationData)
		return translationData
	} catch (error) {
		console.error(`Failed to load locale ${locale}:`, error)
		return {}
	}
}

export async function initI18n(locale: 'en' | 'ja' = 'en') {
	// Load initial translations
	const [enTranslation, jaTranslation] = await Promise.all([
		loadTranslation('en'),
		loadTranslation('ja'),
	])

	i18n.use(initReactI18next).init({
		resources: {
			en: { translation: enTranslation },
			ja: { translation: jaTranslation },
		},
		lng: locale,
		fallbackLng: 'en',
		interpolation: { escapeValue: false },
	})
	return i18n
}

export async function updateLocale(locale: 'en' | 'ja') {
	const translation = await loadTranslation(locale)
	i18n.addResourceBundle(locale, 'translation', translation, true, true)
	await i18n.changeLanguage(locale)
}
