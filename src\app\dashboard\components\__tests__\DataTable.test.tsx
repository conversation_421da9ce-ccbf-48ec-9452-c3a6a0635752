/**
 * Tests for DataTable component
 */

import { render, screen, fireEvent } from '../../../../test-utils'
import DataTable from '../DataTable'
import { mockTasks, mockEmptyTasks } from '../../../../__mocks__/mockData'

describe('DataTable Component', () => {
	describe('when rendering with data', () => {
		it('should display table headers correctly', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByText('Name')).toBeInTheDocument()
			expect(screen.getByText('Planned')).toBeInTheDocument()
			expect(screen.getByText('Actual')).toBeInTheDocument()
		})

		it('should display sub-headers correctly', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByText('No')).toBeInTheDocument()
			expect(screen.getByText('Shipping Date')).toBeInTheDocument()
			expect(screen.getByText('Vangp')).toBeInTheDocument()
			expect(screen.getByText('Delivery Time')).toBeInTheDocument()
			expect(screen.getByText('Start')).toBeInTheDocument()
			expect(screen.getByText('End')).toBeInTheDocument()
			expect(screen.getByText('Duration')).toBeInTheDocument()
		})

		it('should display task data rows', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByText('T001')).toBeInTheDocument()
			expect(screen.getByText('T002')).toBeInTheDocument()
			expect(screen.getByText('T003')).toBeInTheDocument()
		})

		it('should display task details correctly', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByText('2024-01-15')).toBeInTheDocument()
			expect(screen.getByText('VGP001')).toBeInTheDocument()
			expect(screen.getByText('09:00')).toBeInTheDocument()
			expect(screen.getByText('08:00')).toBeInTheDocument()
			expect(screen.getByText('17:00')).toBeInTheDocument()
			expect(screen.getByText('9h')).toBeInTheDocument()
		})
	})

	describe('when rendering with empty data', () => {
		it('should display table structure without data rows', () => {
			// Arrange & Act
			render(<DataTable rows={mockEmptyTasks} />)

			// Assert
			expect(screen.getByRole('table')).toBeInTheDocument()
			expect(screen.getAllByRole('row')).toHaveLength(2) // Only header rows
		})
	})

	describe('row selection functionality', () => {
		it('should call onSelect when row is clicked', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			render(<DataTable rows={mockTasks} onSelect={mockOnSelect} />)

			// Act
			const firstRow = screen.getByText('T001').closest('tr')
			fireEvent.click(firstRow!)

			// Assert
			expect(mockOnSelect).toHaveBeenCalledWith(mockTasks[0])
			expect(mockOnSelect).toHaveBeenCalledTimes(1)
		})

		it('should not call onSelect when onSelect is not provided', () => {
			// Arrange
			render(<DataTable rows={mockTasks} />)

			// Act
			const firstRow = screen.getByText('T001').closest('tr')
			fireEvent.click(firstRow!)

			// Assert
			// Should not throw error and should not have pointer cursor
			expect(firstRow).toHaveStyle({ cursor: 'default' })
		})

		it('should highlight selected row', () => {
			// Arrange
			const selectedRowId = 1
			render(<DataTable rows={mockTasks} selectedRowId={selectedRowId} />)

			// Assert
			const selectedRow = screen.getByText('T001').closest('tr')
			expect(selectedRow).toHaveAttribute('aria-selected', 'true')
		})

		it('should not highlight any row when no selection', () => {
			// Arrange
			render(<DataTable rows={mockTasks} />)

			// Assert
			const rows = screen.getAllByRole('row').slice(2) // Exclude header rows
			rows.forEach((row) => {
				expect(row).not.toHaveAttribute('aria-selected', 'true')
			})
		})
	})

	describe('table styling and behavior', () => {
		it('should have proper table structure', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByRole('table')).toBeInTheDocument()
			expect(screen.getByRole('table')).toHaveAttribute('size', 'small')
		})

		it('should have sticky headers', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			const tableContainer = screen.getByRole('table').closest('.MuiTableContainer-root')
			expect(tableContainer).toBeInTheDocument()
		})

		it('should have proper cursor style when onSelect is provided', () => {
			// Arrange
			const mockOnSelect = jest.fn()
			render(<DataTable rows={mockTasks} onSelect={mockOnSelect} />)

			// Assert
			const firstRow = screen.getByText('T001').closest('tr')
			expect(firstRow).toHaveStyle({ cursor: 'pointer' })
		})
	})

	describe('component memoization', () => {
		it('should not re-render when props are equal', () => {
			// Arrange
			const { rerender } = render(<DataTable rows={mockTasks} selectedRowId={1} />)
			const initialTable = screen.getByRole('table')

			// Act
			rerender(<DataTable rows={mockTasks} selectedRowId={1} />)

			// Assert
			expect(screen.getByRole('table')).toBe(initialTable)
		})

		it('should re-render when rows change', () => {
			// Arrange
			const { rerender } = render(<DataTable rows={mockTasks} />)

			// Act
			rerender(<DataTable rows={mockEmptyTasks} />)

			// Assert
			expect(screen.getAllByRole('row')).toHaveLength(2) // Only header rows
		})

		it('should re-render when selectedRowId changes', () => {
			// Arrange
			const { rerender } = render(<DataTable rows={mockTasks} selectedRowId={1} />)

			// Act
			rerender(<DataTable rows={mockTasks} selectedRowId={2} />)

			// Assert
			const newSelectedRow = screen.getByText('T002').closest('tr')
			expect(newSelectedRow).toHaveAttribute('aria-selected', 'true')
		})
	})

	describe('accessibility', () => {
		it('should have proper table semantics', () => {
			// Arrange & Act
			render(<DataTable rows={mockTasks} />)

			// Assert
			expect(screen.getByRole('table')).toBeInTheDocument()
			expect(screen.getAllByRole('columnheader')).toHaveLength(10)
		})

		it('should have proper row selection state', () => {
			// Arrange
			render(<DataTable rows={mockTasks} selectedRowId={1} />)

			// Assert
			const selectedRow = screen.getByText('T001').closest('tr')
			expect(selectedRow).toHaveAttribute('aria-selected', 'true')
		})
	})

	describe('edge cases', () => {
		it('should handle single row', () => {
			// Arrange
			const singleTask = [mockTasks[0]]
			render(<DataTable rows={singleTask} />)

			// Assert
			expect(screen.getByText('T001')).toBeInTheDocument()
			expect(screen.getAllByRole('row')).toHaveLength(3) // 2 headers + 1 data row
		})

		it('should handle very long task names', () => {
			// Arrange
			const longNameTask = {
				...mockTasks[0],
				name: 'This is a very long task name that might cause layout issues in the table display',
			}
			render(<DataTable rows={[longNameTask]} />)

			// Assert
			expect(screen.getByText(/This is a very long task name/)).toBeInTheDocument()
		})
	})
})
