/**
 * Tests for useOrientation hook
 */

import { renderHook, act } from '@testing-library/react'
import { useOrientation } from '../useOrientation'

// Mock matchMedia
const mockMatchMedia = (matches: boolean) => {
	Object.defineProperty(window, 'matchMedia', {
		writable: true,
		value: jest.fn().mockImplementation((query) => ({
			matches,
			media: query,
			onchange: null,
			addListener: jest.fn(), // Deprecated
			removeListener: jest.fn(), // Deprecated
			addEventListener: jest.fn(),
			removeEventListener: jest.fn(),
			dispatchEvent: jest.fn(),
		})),
	})
}

describe('useOrientation Hook', () => {
	beforeEach(() => {
		// Reset matchMedia mock
		Object.defineProperty(window, 'matchMedia', {
			writable: true,
			value: undefined,
		})
	})

	describe('initial state', () => {
		it('should return landscape orientation when media query matches', () => {
			// Arrange
			mockMatchMedia(true)

			// Act
			const { result } = renderHook(() => useOrientation())

			// Assert
			expect(result.current.isLandscape).toBe(true)
		})

		it('should return portrait orientation when media query does not match', () => {
			// Arrange
			mockMatchMedia(false)

			// Act
			const { result } = renderHook(() => useOrientation())

			// Assert
			expect(result.current.isLandscape).toBe(false)
		})
	})

	describe('orientation change handling', () => {
		it('should update orientation when media query changes', () => {
			// Arrange
			let changeHandler: (event: MediaQueryListEvent) => void
			const mockAddEventListener = jest.fn(
				(event: string, handler: (event: MediaQueryListEvent) => void) => {
					if (event === 'change') {
						changeHandler = handler
					}
				},
			)
			const mockRemoveEventListener = jest.fn()

			Object.defineProperty(window, 'matchMedia', {
				writable: true,
				value: jest.fn().mockImplementation(() => ({
					matches: false,
					media: '(orientation: landscape)',
					onchange: null,
					addListener: jest.fn(),
					removeListener: jest.fn(),
					addEventListener: mockAddEventListener,
					removeEventListener: mockRemoveEventListener,
					dispatchEvent: jest.fn(),
				})),
			})

			// Act
			const { result } = renderHook(() => useOrientation())

			// Assert initial state
			expect(result.current.isLandscape).toBe(false)

			// Simulate orientation change
			act(() => {
				if (changeHandler) {
					changeHandler({ matches: true } as MediaQueryListEvent)
				}
			})

			// Assert updated state
			expect(result.current.isLandscape).toBe(true)
		})

		it('should handle multiple orientation changes', () => {
			// Arrange
			let changeHandler: (event: MediaQueryListEvent) => void
			const mockAddEventListener = jest.fn(
				(event: string, handler: (event: MediaQueryListEvent) => void) => {
					if (event === 'change') {
						changeHandler = handler
					}
				},
			)
			const mockRemoveEventListener = jest.fn()

			Object.defineProperty(window, 'matchMedia', {
				writable: true,
				value: jest.fn().mockImplementation(() => ({
					matches: false,
					media: '(orientation: landscape)',
					onchange: null,
					addListener: jest.fn(),
					removeListener: jest.fn(),
					addEventListener: mockAddEventListener,
					removeEventListener: mockRemoveEventListener,
					dispatchEvent: jest.fn(),
				})),
			})

			// Act
			const { result } = renderHook(() => useOrientation())

			// Assert initial state
			expect(result.current.isLandscape).toBe(false)

			// Simulate first orientation change
			act(() => {
				if (changeHandler) {
					changeHandler({ matches: true } as MediaQueryListEvent)
				}
			})

			// Assert first change
			expect(result.current.isLandscape).toBe(true)

			// Simulate second orientation change
			act(() => {
				if (changeHandler) {
					changeHandler({ matches: false } as MediaQueryListEvent)
				}
			})

			// Assert second change
			expect(result.current.isLandscape).toBe(false)
		})
	})

	describe('event listener management', () => {
		it('should add event listener on mount', () => {
			// Arrange
			const mockAddEventListener = jest.fn()
			const mockRemoveEventListener = jest.fn()

			Object.defineProperty(window, 'matchMedia', {
				writable: true,
				value: jest.fn().mockImplementation(() => ({
					matches: false,
					media: '(orientation: landscape)',
					onchange: null,
					addListener: jest.fn(),
					removeListener: jest.fn(),
					addEventListener: mockAddEventListener,
					removeEventListener: mockRemoveEventListener,
					dispatchEvent: jest.fn(),
				})),
			})

			// Act
			renderHook(() => useOrientation())

			// Assert
			expect(mockAddEventListener).toHaveBeenCalledWith('change', expect.any(Function))
		})

		it('should remove event listener on unmount', () => {
			// Arrange
			const mockAddEventListener = jest.fn()
			const mockRemoveEventListener = jest.fn()

			Object.defineProperty(window, 'matchMedia', {
				writable: true,
				value: jest.fn().mockImplementation(() => ({
					matches: false,
					media: '(orientation: landscape)',
					onchange: null,
					addListener: jest.fn(),
					removeListener: jest.fn(),
					addEventListener: mockAddEventListener,
					removeEventListener: mockRemoveEventListener,
					dispatchEvent: jest.fn(),
				})),
			})

			// Act
			const { unmount } = renderHook(() => useOrientation())
			unmount()

			// Assert
			expect(mockRemoveEventListener).toHaveBeenCalledWith('change', expect.any(Function))
		})
	})

	describe('edge cases', () => {
		it('should handle missing addEventListener method gracefully', () => {
			// Arrange
			Object.defineProperty(window, 'matchMedia', {
				writable: true,
				value: jest.fn().mockImplementation(() => ({
					matches: false,
					media: '(orientation: landscape)',
					onchange: null,
					addListener: jest.fn(),
					removeListener: jest.fn(),
					addEventListener: undefined, // Missing method
					removeEventListener: jest.fn(),
					dispatchEvent: jest.fn(),
				})),
			})

			// Act & Assert - Should not throw error
			expect(() => {
				renderHook(() => useOrientation())
			}).not.toThrow()
		})

		it('should handle missing removeEventListener method gracefully', () => {
			// Arrange
			const mockAddEventListener = jest.fn()
			Object.defineProperty(window, 'matchMedia', {
				writable: true,
				value: jest.fn().mockImplementation(() => ({
					matches: false,
					media: '(orientation: landscape)',
					onchange: null,
					addListener: jest.fn(),
					removeListener: jest.fn(),
					addEventListener: mockAddEventListener,
					removeEventListener: undefined, // Missing method
					dispatchEvent: jest.fn(),
				})),
			})

			// Act
			const { unmount } = renderHook(() => useOrientation())

			// Assert - Should not throw error on unmount
			expect(() => {
				unmount()
			}).not.toThrow()
		})
	})

	describe('return value structure', () => {
		it('should return an object with isLandscape property', () => {
			// Arrange
			mockMatchMedia(false)

			// Act
			const { result } = renderHook(() => useOrientation())

			// Assert
			expect(result.current).toHaveProperty('isLandscape')
			expect(typeof result.current.isLandscape).toBe('boolean')
		})

		it('should maintain consistent return value structure', () => {
			// Arrange
			mockMatchMedia(true)

			// Act
			const { result } = renderHook(() => useOrientation())

			// Assert
			expect(Object.keys(result.current)).toEqual(['isLandscape'])
			expect(result.current.isLandscape).toBe(true)
		})
	})
})
