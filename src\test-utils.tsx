import React from 'react'
import { render } from '@testing-library/react'
import type { RenderOptions } from '@testing-library/react'
import { ThemeProvider } from '@mui/material/styles'
import { createTheme } from '@mui/material/styles'

// Create a test theme
const testTheme = createTheme({
	palette: {
		mode: 'light',
		primary: {
			main: '#1976d2',
		},
		secondary: {
			main: '#dc004e',
		},
	},
})

// Custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
	theme?: typeof testTheme
}

function customRender(ui: React.ReactElement, options: CustomRenderOptions = {}) {
	const { theme = testTheme, ...renderOptions } = options

	const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
		return <ThemeProvider theme={theme}>{children}</ThemeProvider>
	}

	return render(ui, { wrapper: AllTheProviders, ...renderOptions })
}

// Re-export everything
// eslint-disable-next-line react-refresh/only-export-components
export * from '@testing-library/react'

// Override render method
export { customRender as render }

// Export test utilities
export { testTheme }
